/* Home Page Styles - Updated for MainLayout */
.home-content {
  width: 100%;
  height: auto;
  padding: var(--spacing-lg);
  position: relative;
  z-index: 1;
}

.scrollable-sections {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
  height: auto; /* Allow natural height */
  min-height: 500px; /* Minimum height for content */
  overflow-y: auto;
  padding-right: 8px; /* Space for custom scrollbar */
  scroll-behavior: smooth;
}

/* Subtle Scrollbars for Feed */
.scrollable-sections::-webkit-scrollbar {
  width: 6px;
}

.scrollable-sections::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

.scrollable-sections::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  transition: background 0.2s ease;
}

.scrollable-sections::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.15);
}

/* Enhanced Section Slides - Smoother */
.section-slide {
  min-height: 400px;
  background: var(--background);
  border-radius: 16px;
  padding: var(--spacing-xl);
  transition: all var(--transition-normal);
  border: 1px solid rgba(255, 255, 255, 0.04);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(8px);
}

.section-slide.neu {
  box-shadow: var(--shadow-neumorphic-soft);
}

.section-slide::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(51, 189, 148, 0.3), transparent);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.section-slide:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-neumorphic-elevated);
  border-color: rgba(255, 255, 255, 0.06);
}

.section-slide:hover::before {
  opacity: 1;
}

.section-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  font-size: 24px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xl);
  padding-bottom: var(--spacing-md);
  border-bottom: 2px solid rgba(51, 189, 148, 0.3);
}

.section-title svg {
  color: var(--accent-color);
  filter: drop-shadow(0 0 8px rgba(51, 189, 148, 0.4));
}

/* Stories Container - Smoother */
.stories-container {
  background: var(--secondary-bg);
  border-radius: 16px;
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
  border: 1px solid rgba(255, 255, 255, 0.04);
  backdrop-filter: blur(8px);
}

.stories-container.neu {
  box-shadow: var(--shadow-neumorphic-soft);
}

.stories-scroll {
  display: flex;
  gap: var(--spacing-md);
  overflow-x: auto;
  padding: var(--spacing-sm) 0;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.stories-scroll::-webkit-scrollbar {
  display: none;
}

.story-item {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
  cursor: pointer;
  transition: transform var(--transition-fast);
}

.story-item:hover {
  transform: translateY(-4px);
}

.story-avatar-border {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  padding: 3px;
  background: linear-gradient(45deg, var(--accent-color), #2da87a);
  box-shadow: var(--shadow-neumorphic);
  transition: all var(--transition-normal);
}

.story-avatar-border.has-story {
  background: linear-gradient(45deg, var(--accent-color), #ff6b6b, #4ecdc4);
  animation: pulse 2s infinite;
}

.story-avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--background);
}

.story-username {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-secondary);
  text-align: center;
  max-width: 70px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Feed Styles */
.feed {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

/* Card Component Styling - Refined Neumorphic Design */
.post,
.neomorphic-card,
.card {
  background: var(--secondary-bg);
  border-radius: 16px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.04);
  transition: all var(--transition-normal);
  position: relative;
  backdrop-filter: blur(8px);
}

.post.neu,
.neomorphic-card.neu,
.card.neu {
  box-shadow: var(--shadow-neumorphic-soft);
}

.post::before,
.neomorphic-card::before,
.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(51, 189, 148, 0.3), transparent);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.post:hover,
.neomorphic-card:hover,
.card:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-neumorphic-elevated);
  border-color: rgba(255, 255, 255, 0.06);
}

.post:hover::before,
.neomorphic-card:hover::before,
.card:hover::before {
  opacity: 1;
}

.post-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg);
}

.post-author {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.avatar {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--accent-color);
  box-shadow: 0 0 10px rgba(51, 189, 148, 0.3);
}

.author-info {
  display: flex;
  flex-direction: column;
}

.author-name {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-weight: 600;
  color: var(--text-primary);
  font-size: 16px;
}

.verified-badge {
  color: var(--accent-color);
  font-size: 14px;
}

.post-time {
  color: var(--text-secondary);
  font-size: 14px;
}

.more-options {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: 50%;
  transition: all var(--transition-fast);
}

.more-options:hover {
  background: var(--background);
  color: var(--text-primary);
  box-shadow: var(--shadow-neumorphic);
}

.post-image {
  width: 100%;
  position: relative;
}

.post-image img {
  width: 100%;
  height: 300px;
  object-fit: cover;
  transition: transform var(--transition-normal);
}

.post:hover .post-image img {
  transform: scale(1.02);
}

.post-content {
  padding: var(--spacing-lg);
}

.post-title {
  font-size: 20px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
  line-height: 1.3;
}

.post-description {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 0;
}

.post-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md) var(--spacing-lg);
  border-top: 1px solid rgba(255, 255, 255, 0.08);
}

.action-buttons {
  display: flex;
  gap: var(--spacing-lg);
}

.action-btn {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  font-size: 18px;
  padding: var(--spacing-sm);
  border-radius: 50%;
  transition: all var(--transition-fast);
  position: relative;
}

.action-btn:hover {
  background: var(--background);
  color: var(--accent-color);
  box-shadow: var(--shadow-neumorphic);
  transform: scale(1.1);
}

.like-btn.liked {
  color: #ff6b6b;
  animation: likeAnimation 0.3s ease;
}

.post-stats {
  padding: 0 var(--spacing-lg) var(--spacing-md);
}

.likes-count {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 14px;
}

/* Comments Section */
.comments-section {
  border-top: 1px solid rgba(255, 255, 255, 0.08);
  padding: var(--spacing-lg);
  background: var(--background);
}

.comments-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
  max-height: 300px;
  overflow-y: auto;
  padding-right: 8px; /* Space for custom scrollbar */
}

/* Subtle Scrollbars for Comments */
.comments-list::-webkit-scrollbar {
  width: 6px;
}

.comments-list::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

.comments-list::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  transition: background 0.2s ease;
}

.comments-list::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.15);
}

.comment {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: var(--spacing-sm);
  background: var(--secondary-bg);
  border-radius: 12px;
  box-shadow: 
    inset 1px 1px 2px rgba(0, 0, 0, 0.1),
    inset -1px -1px 2px rgba(255, 255, 255, 0.02);
  transition: all var(--transition-fast);
}

.comment:hover {
  box-shadow: 
    inset 1px 1px 3px rgba(0, 0, 0, 0.2),
    inset -1px -1px 3px rgba(255, 255, 255, 0.04);
}

.comment-content {
  flex: 1;
}

.comment-username {
  font-weight: 600;
  color: var(--accent-color);
  margin-right: var(--spacing-sm);
  font-size: 14px;
}

.comment-text {
  color: var(--text-primary);
  font-size: 14px;
}

.comment-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-xs);
}

.comment-time {
  color: var(--text-secondary);
  font-size: 12px;
}

.comment-like-btn,
.comment-delete-btn {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  font-size: 12px;
  padding: 2px;
  transition: color var(--transition-fast);
}

.comment-like-btn:hover,
.comment-delete-btn:hover {
  color: var(--accent-color);
}

.comment-form {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
}

.comment-input {
  flex: 1;
  background: var(--secondary-bg);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  padding: var(--spacing-sm) var(--spacing-md);
  color: var(--text-primary);
  font-size: 14px;
  box-shadow: 
    inset 1px 1px 2px rgba(0, 0, 0, 0.1),
    inset -1px -1px 2px rgba(255, 255, 255, 0.02);
}

.comment-input:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 
    inset 1px 1px 3px rgba(0, 0, 0, 0.2),
    inset -1px -1px 3px rgba(255, 255, 255, 0.04),
    0 0 0 2px rgba(51, 189, 148, 0.3);
}

.comment-submit {
  background: var(--accent-color);
  color: var(--background);
  border: none;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
}

/* Code Editor Section - Smoother */
.code-editor-section {
  background: var(--secondary-bg);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.04);
  overflow: hidden;
  transition: all var(--transition-normal);
  margin-bottom: var(--spacing-lg);
  backdrop-filter: blur(8px);
}

.code-editor-section.neu {
  box-shadow: var(--shadow-neumorphic-soft);
}

.code-editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.02) 0%, 
    rgba(255, 255, 255, 0.005) 100%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.06);
}

.code-editor-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.code-editor-controls {
  display: flex;
  gap: var(--spacing-sm);
}

.code-control-btn {
  background: rgba(255, 255, 255, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.06);
  color: var(--text-secondary);
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 12px;
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  box-shadow: var(--shadow-neumorphic-inset);
}

.code-control-btn:hover {
  background: var(--accent-color);
  color: var(--background);
  border-color: var(--accent-color);
  transform: translateY(-1px);
  box-shadow: 
    0 3px 8px rgba(51, 189, 148, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.code-control-btn:active {
  transform: translateY(0);
  box-shadow: var(--shadow-neumorphic-inset);
}

.code-editor-content {
  padding: var(--spacing-lg);
  background: #0d1117;
  color: #c9d1d9;
  font-family: 'Fira Code', Monaco, 'Cascadia Code', 'Roboto Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
  overflow-x: auto;
  max-height: 400px;
  overflow-y: auto;
  padding-right: 8px; /* Space for custom scrollbar */
}

/* Subtle Scrollbars for Code Editor */
.code-editor-content::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.code-editor-content::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

.code-editor-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  transition: background 0.2s ease;
}

.code-editor-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.15);
}

.code-line {
  display: flex;
  align-items: flex-start;
  min-height: 21px;
  padding: 2px 0;
  transition: background var(--transition-fast);
}

.code-line:hover {
  background: rgba(255, 255, 255, 0.02);
}

.line-number {
  color: #7d8590;
  font-size: 12px;
  margin-right: var(--spacing-md);
  user-select: none;
  min-width: 25px;
  text-align: right;
  flex-shrink: 0;
}

/* Syntax Highlighting */
.keyword { color: #ff7b72; font-weight: 600; }
.string { color: #a5d6ff; }
.comment { color: #8b949e; font-style: italic; }
.function { color: #d2a8ff; font-weight: 500; }
.variable { color: #ffa657; }
.number { color: #79c0ff; }
.operator { color: #ff7b72; }

/* Code Tabs */
.code-tabs {
  display: flex;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-lg);
}

.code-tab {
  background: var(--background);
  border: 1px solid rgba(255, 255, 255, 0.08);
  color: var(--text-secondary);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: 12px;
  font-size: 14px;
  cursor: pointer;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-neumorphic);
}

.code-tab:hover {
  background: var(--secondary-bg);
  color: var(--text-primary);
  transform: translateY(-1px);
}

.code-tab.btn-primary {
  background: var(--accent-color);
  color: var(--background);
  border-color: var(--accent-color);
  box-shadow: 
    0 4px 8px rgba(51, 189, 148, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Animations */
@keyframes likeAnimation {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* Responsive Design */
@media (max-width: 768px) {
  .home-content {
    padding: var(--spacing-md);
  }
  
  .section-slide {
    padding: var(--spacing-lg);
    min-height: 300px;
  }
  
  .scrollable-sections {
    max-height: calc(100vh - 150px);
    padding-right: 4px;
  }
  
  .stories-scroll {
    gap: var(--spacing-sm);
  }
  
  .story-avatar-border {
    width: 60px;
    height: 60px;
  }
  
  .story-username {
    max-width: 60px;
  }
  
  .post-image img {
    height: 250px;
  }
  
  .code-editor-controls {
    flex-wrap: wrap;
  }
  
  .code-control-btn {
    font-size: 11px;
  }
  
  .code-tabs {
    flex-wrap: wrap;
  }
}

/* Post Gallery Styles */
.post-gallery {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-lg);
  margin-top: var(--spacing-lg);
}

@media (max-width: 768px) {
  .post-gallery {
    grid-template-columns: 1fr;
  }
}

.post-gallery-card {
  cursor: pointer;
  transition: all var(--transition-normal);
}

.post-gallery-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-neumorphic-elevated);
}

.post-description-truncated {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Drawer Overlay */
.drawer-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(8px);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Post Drawer */
.post-drawer {
  position: fixed;
  top: 0;
  right: 0;
  width: min(680px, 100%);
  height: 100vh;
  background: var(--secondary-bg);
  border-left: 1px solid rgba(255, 255, 255, 0.08);
  box-shadow: var(--shadow-neumorphic-elevated);
  overflow-y: auto;
  animation: slideInRight 0.3s ease;
  display: flex;
  flex-direction: column;
}

@keyframes slideInRight {
  from { transform: translateX(100%); }
  to { transform: translateX(0); }
}

/* Code Drawer */
.code-drawer {
  width: 90vw;
  max-width: 1200px;
  height: 90vh;
  background: var(--secondary-bg);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.08);
  box-shadow: var(--shadow-neumorphic-elevated);
  overflow: hidden;
  animation: scaleIn 0.3s ease;
  display: flex;
  flex-direction: column;
}

@keyframes scaleIn {
  from { transform: scale(0.9); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

/* Drawer Header */
.drawer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg);
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.02) 0%,
    rgba(255, 255, 255, 0.005) 100%);
  flex-shrink: 0;
}

.drawer-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.drawer-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.drawer-close {
  background: rgba(255, 255, 255, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.08);
  color: var(--text-secondary);
  padding: var(--spacing-sm);
  border-radius: 8px;
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
}

.drawer-close:hover {
  background: rgba(255, 0, 0, 0.1);
  color: #ff6b6b;
  border-color: rgba(255, 107, 107, 0.3);
}

/* Drawer Content */
.drawer-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-lg);
}

.post-drawer .drawer-content {
  padding: 0;
}

.post-drawer .post {
  border-radius: 0;
  border: none;
  box-shadow: none;
  margin: 0;
}

.code-drawer .drawer-content {
  padding: 0;
}

.code-drawer .code-editor-content {
  height: 100%;
  max-height: none;
  border-radius: 0;
  margin: 0;
}

/* Responsive Drawer Styles */
@media (max-width: 768px) {
  .post-drawer {
    width: 100%;
  }

  .code-drawer {
    width: 95vw;
    height: 95vh;
  }

  .drawer-header {
    padding: var(--spacing-md);
  }

  .drawer-content {
    padding: var(--spacing-md);
  }
}

/* Full-Feed Expansion Styles */
.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-xl);
}

/* Enhanced Feed Toggle Button */
.feed-toggle {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 14px;
  background: var(--accent-color);
  color: var(--background);
  border-radius: 8px;
  font-weight: 600;
  font-size: 13px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.feed-toggle:hover {
  filter: brightness(1.1);
  transform: translateY(-1px);
}

/* Enhanced Full-Feed Layout */
.feed-expanded .content-column {
  max-width: 1180px;
}

.feed-expanded .sidebar-column {
  display: none;
}

.feed-expanded .post-gallery {
  grid-template-columns: repeat(auto-fill, minmax(460px, 1fr));
}

.feed-expanded .post-gallery-card .post-image img {
  height: 260px;
  object-fit: cover;
}

@media (max-width: 800px) {
  .feed-expanded .post-gallery {
    grid-template-columns: 1fr;
  }
}

/* Full-Width Feed v2 Styles */
/* feed expanded → single column, bigger cards */
.post-gallery.wide {
  display: grid;
  grid-template-columns: 1fr;     /* one column */
  gap: var(--spacing-lg);
  max-height: calc(100vh - 200px); /* Set maximum height for scrolling */
  overflow-y: auto;                /* Enable vertical scrolling */
  padding-right: 8px;              /* Space for custom scrollbar */
  scroll-behavior: smooth;         /* Smooth scrolling */
}

/* Custom scrollbar for expanded feed */
.post-gallery.wide::-webkit-scrollbar {
  width: 8px;
}

.post-gallery.wide::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

.post-gallery.wide::-webkit-scrollbar-thumb {
  background: rgba(51, 189, 148, 0.3);
  border-radius: 4px;
  transition: background 0.2s ease;
}

.post-gallery.wide::-webkit-scrollbar-thumb:hover {
  background: rgba(51, 189, 148, 0.5);
}

.post-gallery.wide .post-gallery-card {
  max-width: 680px;               /* image & caption breathe */
  margin: 0 auto;
}

/* collapse: restore two-up masonry */
@media(min-width: 1024px) {
  .post-gallery:not(.wide) {
    grid-template-columns: repeat(auto-fill, minmax(440px, 1fr));
  }
}

/* hide RHS sidebar when expanded */
.feed-expanded .sidebar-column {
  display: none;
}

.feed-expanded .content-column {
  max-width: 1280px;
}

/* Enhanced scrollable container for expanded feed */
.feed-expanded .scrollable-sections {
  height: calc(100vh - 120px); /* Full height minus header/padding */
  overflow-y: auto;
  padding-right: 8px;
}

/* Expanded feed container styling */
.expanded-feed-container {
  position: relative;
  width: 100%;
  height: calc(100vh - 280px); /* Adjust based on header and section padding */
  overflow: hidden;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.04);
}

/* Enhanced styling for expanded feed posts */
.expanded-feed-container .post-gallery-card {
  transition: all var(--transition-normal);
  cursor: pointer;
}

.expanded-feed-container .post-gallery-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-neumorphic-elevated);
}

/* Responsive adjustments for expanded feed */
@media (max-width: 768px) {
  .post-gallery.wide {
    max-height: calc(100vh - 160px);
    padding-right: 4px;
  }

  .feed-expanded .scrollable-sections {
    height: calc(100vh - 140px);
    padding-right: 4px;
  }

  .expanded-feed-container {
    height: calc(100vh - 320px);
  }
}

/* Instagram-Style Post Overlay */
.post-overlay {
  position: fixed;
  inset: 0;
  z-index: 1000;
  background: rgba(0, 0, 0, 0.85);
  display: flex;
  justify-content: center;
  align-items: center;
  backdrop-filter: blur(8px);
  animation: fadeIn 0.3s ease;
}

.overlay-inner {
  display: flex;
  gap: 32px;
  max-width: 1200px;
  width: 90%;
  height: 85vh;
  max-height: 800px;
}

.overlay-image {
  flex: 2;
  display: flex;
  align-items: center;
  justify-content: center;
}

.overlay-image img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 12px;
}

.overlay-meta {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: var(--secondary-bg);
  border-radius: 12px;
  padding: 24px;
  overflow-y: auto;
  border: 1px solid rgba(255, 255, 255, 0.08);
}

.overlay-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
}

.overlay-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.overlay-author-info h4 {
  margin: 0;
  color: var(--text-primary);
  font-size: 16px;
  font-weight: 600;
}

.overlay-author-info span {
  color: var(--text-secondary);
  font-size: 14px;
}

.overlay-content {
  flex: 1;
  margin-bottom: 20px;
}

.overlay-content h3 {
  color: var(--text-primary);
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 12px;
}

.overlay-content p {
  color: var(--text-secondary);
  line-height: 1.6;
}

.overlay-actions {
  display: flex;
  gap: 16px;
  padding: 16px 0;
  border-top: 1px solid rgba(255, 255, 255, 0.08);
}

.overlay-stats {
  color: var(--text-primary);
  font-weight: 600;
  font-size: 14px;
}

.overlay-close {
  position: absolute;
  top: 20px;
  right: 24px;
  font-size: 32px;
  background: rgba(0, 0, 0, 0.5);
  border: none;
  color: #fff;
  cursor: pointer;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s ease;
}

.overlay-close:hover {
  background: rgba(0, 0, 0, 0.7);
}

/* Responsive Overlay */
@media (max-width: 768px) {
  .overlay-inner {
    flex-direction: column;
    width: 95%;
    height: 90vh;
    gap: 16px;
  }

  .overlay-image {
    flex: 1;
  }

  .overlay-meta {
    flex: 1;
    padding: 16px;
  }
}